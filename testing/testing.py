#!/usr/bin/env python3
# -*- coding: UTF-8 -*-

"""
文件名：number_sum_calculator.py
作者：DevOps工程师
日期：2024年1月15日
功能：计算两个数字的算术和
版本：V1.0
参数解释：--num1 第一个数字参数，--num2 第二个数字参数
适用产品：通用计算工具
适用环境：Python 3.6及以上版本，跨平台兼容
"""

import argparse
import sys
import logging
from typing import Union, Tuple

# 全局变量定义
V_SCRIPT_NAME = "number_sum_calculator"
V_SCRIPT_VERSION = "V1.0"
V_MAX_NUMBER_LENGTH = 50

# 日志配置
def setup_logging() -> None:
    """配置日志系统"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(module)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )

def validate_number(number_str: str) -> Union[int, float]:
    """
    验证并转换数字字符串
    
    Args:
        number_str: 数字字符串
        
    Returns:
        转换后的数字（整数或浮点数）
        
    Raises:
        ValueError: 当输入不是有效数字时
    """
    if not number_str:
        raise ValueError("输入参数不能为空")
    
    if len(number_str) > V_MAX_NUMBER_LENGTH:
        raise ValueError(f"数字长度超过最大限制 {V_MAX_NUMBER_LENGTH}")
    
    # 检查特殊字符和潜在的安全风险
    if any(char in number_str for char in [';', '\n', '\r', '|', '&', '$', '`']):
        raise ValueError("输入包含非法字符")
    
    try:
        # 尝试转换为整数
        return int(number_str)
    except ValueError:
        try:
            # 尝试转换为浮点数
            return float(number_str)
        except ValueError:
            raise ValueError(f"'{number_str}' 不是有效的数字格式")

def parse_arguments() -> Tuple[Union[int, float], Union[int, float]]:
    """
    解析命令行参数
    
    Returns:
        包含两个数字的元组
        
    Raises:
        SystemExit: 当参数验证失败时
    """
    parser = argparse.ArgumentParser(
        description='计算两个数字的算术和',
        prog=V_SCRIPT_NAME
    )
    
    parser.add_argument(
        '--num1',
        type=str,
        required=True,
        help='第一个数字参数（整数或浮点数）'
    )
    
    parser.add_argument(
        '--num2',
        type=str,
        required=True,
        help='第二个数字参数（整数或浮点数）'
    )
    
    parser.add_argument(
        '--version',
        action='version',
        version=f'{V_SCRIPT_NAME} {V_SCRIPT_VERSION}',
        help='显示脚本版本信息'
    )
    
    try:
        args = parser.parse_args()
        
        # 验证数字参数
        num1 = validate_number(args.num1)
        num2 = validate_number(args.num2)
        
        return num1, num2
        
    except ValueError as e:
        logging.error(f"参数验证失败: {e}")
        parser.print_help()
        sys.exit(1)
    except SystemExit:
        # 当用户请求 --help 或 --version 时正常退出
        sys.exit(0)

def check_environment() -> None:
    """检查运行环境"""
    if sys.version_info < (3, 6):
        logging.error("需要Python 3.6或更高版本")
        sys.exit(127)

def calculate_sum(num1: Union[int, float], num2: Union[int, float]) -> Union[int, float]:
    """
    计算两个数字的和
    
    Args:
        num1: 第一个数字
        num2: 第二个数字
        
    Returns:
        两个数字的和
    """
    try:
        result = num1 + num2
        logging.info(f"计算完成: {num1} + {num2} = {result}")
        return result
    except OverflowError:
        logging.error("计算结果超出Python数值范围")
        sys.exit(1)
    except Exception as e:
        logging.error(f"计算过程中发生未预期错误: {e}")
        sys.exit(127)

def main() -> None:
    """主函数"""
    setup_logging()
    logging.info(f"{V_SCRIPT_NAME} 开始执行")
    
    try:
        # 环境检查
        check_environment()
        
        # 参数解析和验证
        num1, num2 = parse_arguments()
        
        # 执行计算
        result = calculate_sum(num1, num2)
        
        # 输出结果
        print(result)
        logging.info(f"脚本执行成功，结果: {result}")
        
    except KeyboardInterrupt:
        logging.warning("用户中断脚本执行")
        sys.exit(130)
    except Exception as e:
        logging.error(f"脚本执行失败: {e}")
        sys.exit(127)

if __name__ == "__main__":
    main()