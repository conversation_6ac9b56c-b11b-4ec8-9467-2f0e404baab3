#!/bin/bash

set -euo pipefail

# 文件名：number_sum_calculator.sh
# 作者：DevOps工程师
# 日期：2024年1月15日
# 功能：计算两个整数的和，提供完整的参数验证和错误处理
# 版本：V1.0
# 参数解释：--num1 第一个整数加数，--num2 第二个整数加数
# 适用产品：通用Linux环境
# 适用环境：支持bash 4.0+的Linux/Unix系统

readonly V_SCRIPT_NAME="number_sum_calculator"
readonly V_SCRIPT_VERSION="1.0"
readonly V_MIN_BASH_VERSION="4.0"

# 全局变量定义
V_LOG_LEVEL="${LOG_LEVEL:-INFO}"
V_NUM1=""
V_NUM2=""
V_RESULT=""

# 日志级别枚举
readonly V_LOG_DEBUG="DEBUG"
readonly V_LOG_INFO="INFO"
readonly V_LOG_WARN="WARN"
readonly V_LOG_ERROR="ERROR"
readonly V_LOG_FATAL="FATAL"

# 退出码定义
readonly V_EXIT_SUCCESS=0
readonly V_EXIT_PARAM_ERROR=1
readonly V_EXIT_ENV_ERROR=2
readonly V_EXIT_CALC_ERROR=3
readonly V_EXIT_UNKNOWN_ERROR=127

# 日志记录函数
log_message() {
    local v_level="$1"
    local v_message="$2"
    local v_function="${3:-main}"
    local v_timestamp=$(date -u +"%Y-%m-%dT%H:%M:%S.%3NZ")
    
    # 日志级别过滤
    case "${V_LOG_LEVEL}" in
        "DEBUG") ;;
        "INFO") [[ "${v_level}" == "DEBUG" ]] && return ;;
        "WARN") [[ "${v_level}" == "DEBUG" || "${v_level}" == "INFO" ]] && return ;;
        "ERROR") [[ "${v_level}" != "ERROR" && "${v_level}" != "FATAL" ]] && return ;;
        "FATAL") [[ "${v_level}" != "FATAL" ]] && return ;;
    esac
    
    echo "${v_timestamp} - ${v_level} - ${V_SCRIPT_NAME} - ${v_function} - ${v_message}" >&2
}

# 清理函数
cleanup() {
    log_message "${V_LOG_DEBUG}" "执行清理操作" "cleanup"
    # 清理临时资源
    return 0
}

# 错误处理函数
error_exit() {
    local v_exit_code="$1"
    local v_message="$2"
    local v_function="${3:-main}"
    
    log_message "${V_LOG_ERROR}" "${v_message}" "${v_function}"
    cleanup
    exit "${v_exit_code}"
}

# 显示使用说明
show_usage() {
    cat << EOF
${V_SCRIPT_NAME} - 数字求和计算器 v${V_SCRIPT_VERSION}

用法: ${0} --num1 <数字1> --num2 <数字2>

参数:
  --num1      第一个整数加数 (必填)
  --num2      第二个整数加数 (必填)
  --version   显示版本信息
  --help      显示帮助信息

环境变量:
  LOG_LEVEL   日志级别 (DEBUG/INFO/WARN/ERROR/FATAL), 默认: INFO

示例:
  ${0} --num1 10 --num2 20
  LOG_LEVEL=DEBUG ${0} --num1 -5 --num2 15

退出码:
  0 - 成功
  1 - 参数错误
  2 - 环境依赖错误
  3 - 计算错误
  127 - 未知错误
EOF
}



# 检查环境依赖
check_dependencies() {
    local v_dependencies=("bc" "date")
    local v_dep
    
    for v_dep in "${v_dependencies[@]}"; do
        if ! command -v "${v_dep}" >/dev/null 2>&1; then
            error_exit "${V_EXIT_ENV_ERROR}" "依赖命令不存在: ${v_dep}" "check_dependencies"
        fi
    done
    
    log_message "${V_LOG_DEBUG}" "所有依赖命令检查通过" "check_dependencies"
}

# 验证数字格式
validate_number() {
    local v_number="$1"
    local v_param_name="$2"
    
    # 检查是否为空
    if [[ -z "${v_number}" ]]; then
        error_exit "${V_EXIT_PARAM_ERROR}" "参数 ${v_param_name} 不能为空" "validate_number"
    fi
    
    # 检查数字格式 (支持正负整数)
    if ! [[ "${v_number}" =~ ^-?[0-9]+$ ]]; then
        error_exit "${V_EXIT_PARAM_ERROR}" "参数 ${v_param_name} 必须是整数: ${v_number}" "validate_number"
    fi
    
    log_message "${V_LOG_DEBUG}" "参数 ${v_param_name} 验证通过: ${v_number}" "validate_number"
}

# 解析命令行参数
parse_arguments() {
    local v_num1_provided=false
    local v_num2_provided=false
    
    while [[ $# -gt 0 ]]; do
        case "$1" in
            --num1)
                if [[ -z "${2:-}" || "${2}" =~ ^- ]]; then
                    error_exit "${V_EXIT_PARAM_ERROR}" "参数 --num1 需要提供一个值" "parse_arguments"
                fi
                V_NUM1="$2"
                v_num1_provided=true
                shift 2
                ;;
            --num2)
                if [[ -z "${2:-}" || "${2}" =~ ^- ]]; then
                    error_exit "${V_EXIT_PARAM_ERROR}" "参数 --num2 需要提供一个值" "parse_arguments"
                fi
                V_NUM2="$2"
                v_num2_provided=true
                shift 2
                ;;
            --version)
                echo "${V_SCRIPT_NAME} v${V_SCRIPT_VERSION}"
                exit "${V_EXIT_SUCCESS}"
                ;;
            --help)
                show_usage
                exit "${V_EXIT_SUCCESS}"
                ;;
            -*)
                error_exit "${V_EXIT_PARAM_ERROR}" "未知参数: $1" "parse_arguments"
                ;;
            *)
                error_exit "${V_EXIT_PARAM_ERROR}" "无效参数: $1" "parse_arguments"
                ;;
        esac
    done
    
    # 检查必填参数
    if [[ "${v_num1_provided}" != "true" ]]; then
        error_exit "${V_EXIT_PARAM_ERROR}" "缺少必填参数: --num1" "parse_arguments"
    fi
    
    if [[ "${v_num2_provided}" != "true" ]]; then
        error_exit "${V_EXIT_PARAM_ERROR}" "缺少必填参数: --num2" "parse_arguments"
    fi
    
    # 验证参数格式
    validate_number "${V_NUM1}" "--num1"
    validate_number "${V_NUM2}" "--num2"
    
    log_message "${V_LOG_INFO}" "参数解析完成: num1=${V_NUM1}, num2=${V_NUM2}" "parse_arguments"
}

# 计算两个数字的和
calculate_sum() {
    local v_num1="$1"
    local v_num2="$2"
    local v_result
    
    log_message "${V_LOG_DEBUG}" "开始计算: ${v_num1} + ${v_num2}" "calculate_sum"
    
    # 使用bc进行安全计算
    if ! v_result=$(echo "${v_num1} + ${v_num2}" | bc 2>/dev/null); then
        error_exit "${V_EXIT_CALC_ERROR}" "计算失败: ${v_num1} + ${v_num2}" "calculate_sum"
    fi
    
    # 检查计算结果是否为整数
    if ! [[ "${v_result}" =~ ^-?[0-9]+$ ]]; then
        error_exit "${V_EXIT_CALC_ERROR}" "计算结果不是整数: ${v_result}" "calculate_sum"
    fi
    
    log_message "${V_LOG_DEBUG}" "计算完成: ${v_num1} + ${v_num2} = ${v_result}" "calculate_sum"
    echo "${v_result}"
}

# 主执行函数
main() {
    local v_start_time=$(date -u +"%Y-%m-%dT%H:%M:%S.%3NZ")
    local v_execution_user=$(whoami)
    local v_exit_code="${V_EXIT_SUCCESS}"
    
    trap 'error_exit "${V_EXIT_UNKNOWN_ERROR}" "脚本被中断" "main"' INT TERM
    
    log_message "${V_LOG_INFO}" "脚本启动" "main"
    log_message "${V_LOG_DEBUG}" "开始时间: ${v_start_time}, 执行用户: ${v_execution_user}" "main"
    
    # 环境检查
    check_dependencies
    
    # 参数解析
    parse_arguments "$@"
    
    # 执行计算
    V_RESULT=$(calculate_sum "${V_NUM1}" "${V_NUM2}")
    
    # 输出结果
    echo "${V_RESULT}"
    
    local v_end_time=$(date -u +"%Y-%m-%dT%H:%M:%S.%3NZ")
    log_message "${V_LOG_INFO}" "脚本执行成功, 结果: ${V_RESULT}" "main"
    log_message "${V_LOG_DEBUG}" "结束时间: ${v_end_time}, 执行时长: $(($(date +%s) - $(date -d "${v_start_time}" +%s)))秒" "main"
    
    exit "${v_exit_code}"
}

# 脚本入口
main "$@"