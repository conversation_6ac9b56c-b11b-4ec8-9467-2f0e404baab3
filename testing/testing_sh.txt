"#!/bin/bash\n\nset -euo pipefail\n\n# 文件名：number_sum_calculator.sh\n# 作者：DevOps工程师\n# 日期：2024年1月15日\n# 功能：计算两个整数的和，提供完整的参数验证和错误处理\n# 版本：V1.0\n# 参数解释：--num1 第一个整数加数，--num2 第二个整数加数\n# 适用产品：通用Linux环境\n# 适用环境：支持bash 4.0+的Linux/Unix系统\n\nreadonly V_SCRIPT_NAME=\"number_sum_calculator\"\nreadonly V_SCRIPT_VERSION=\"1.0\"\nreadonly V_MIN_BASH_VERSION=\"4.0\"\n\n# 全局变量定义\nV_LOG_LEVEL=\"${LOG_LEVEL:-INFO}\"\nV_NUM1=\"\"\nV_NUM2=\"\"\nV_RESULT=\"\"\n\n# 日志级别枚举\nreadonly V_LOG_DEBUG=\"DEBUG\"\nreadonly V_LOG_INFO=\"INFO\"\nreadonly V_LOG_WARN=\"WARN\"\nreadonly V_LOG_ERROR=\"ERROR\"\nreadonly V_LOG_FATAL=\"FATAL\"\n\n# 退出码定义\nreadonly V_EXIT_SUCCESS=0\nreadonly V_EXIT_PARAM_ERROR=1\nreadonly V_EXIT_ENV_ERROR=2\nreadonly V_EXIT_CALC_ERROR=3\nreadonly V_EXIT_UNKNOWN_ERROR=127\n\n# 日志记录函数\nlog_message() {\n    local v_level=\"$1\"\n    local v_message=\"$2\"\n    local v_function=\"${3:-main}\"\n    local v_timestamp=$(date -u +\"%Y-%m-%dT%H:%M:%S.%3NZ\")\n    \n    # 日志级别过滤\n    case \"${V_LOG_LEVEL}\" in\n        \"DEBUG\") ;;\n        \"INFO\") [[ \"${v_level}\" == \"DEBUG\" ]] && return ;;\n        \"WARN\") [[ \"${v_level}\" == \"DEBUG\" || \"${v_level}\" == \"INFO\" ]] && return ;;\n        \"ERROR\") [[ \"${v_level}\" != \"ERROR\" && \"${v_level}\" != \"FATAL\" ]] && return ;;\n        \"FATAL\") [[ \"${v_level}\" != \"FATAL\" ]] && return ;;\n    esac\n    \n    echo \"${v_timestamp} - ${v_level} - ${V_SCRIPT_NAME} - ${v_function} - ${v_message}\" >&2\n}\n\n# 清理函数\ncleanup() {\n    log_message \"${V_LOG_DEBUG}\" \"执行清理操作\" \"cleanup\"\n    # 清理临时资源\n    return 0\n}\n\n# 错误处理函数\nerror_exit() {\n    local v_exit_code=\"$1\"\n    local v_message=\"$2\"\n    local v_function=\"${3:-main}\"\n    \n    log_message \"${V_LOG_ERROR}\" \"${v_message}\" \"${v_function}\"\n    cleanup\n    exit \"${v_exit_code}\"\n}\n\n# 显示使用说明\nshow_usage() {\n    cat << EOF\n${V_SCRIPT_NAME} - 数字求和计算器 v${V_SCRIPT_VERSION}\n\n用法: ${0} --num1 <数字1> --num2 <数字2>\n\n参数:\n  --num1      第一个整数加数 (必填)\n  --num2      第二个整数加数 (必填)\n  --version   显示版本信息\n  --help      显示帮助信息\n\n环境变量:\n  LOG_LEVEL   日志级别 (DEBUG/INFO/WARN/ERROR/FATAL), 默认: INFO\n\n示例:\n  ${0} --num1 10 --num2 20\n  LOG_LEVEL=DEBUG ${0} --num1 -5 --num2 15\n\n退出码:\n  0 - 成功\n  1 - 参数错误\n  2 - 环境依赖错误\n  3 - 计算错误\n  127 - 未知错误\nEOF\n}\n\n\n\n# 检查环境依赖\ncheck_dependencies() {\n    local v_dependencies=(\"bc\" \"date\")\n    local v_dep\n    \n    for v_dep in \"${v_dependencies[@]}\"; do\n        if ! command -v \"${v_dep}\" >/dev/null 2>&1; then\n            error_exit \"${V_EXIT_ENV_ERROR}\" \"依赖命令不存在: ${v_dep}\" \"check_dependencies\"\n        fi\n    done\n    \n    log_message \"${V_LOG_DEBUG}\" \"所有依赖命令检查通过\" \"check_dependencies\"\n}\n\n# 验证数字格式\nvalidate_number() {\n    local v_number=\"$1\"\n    local v_param_name=\"$2\"\n    \n    # 检查是否为空\n    if [[ -z \"${v_number}\" ]]; then\n        error_exit \"${V_EXIT_PARAM_ERROR}\" \"参数 ${v_param_name} 不能为空\" \"validate_number\"\n    fi\n    \n    # 检查数字格式 (支持正负整数)\n    if ! [[ \"${v_number}\" =~ ^-?[0-9]+$ ]]; then\n        error_exit \"${V_EXIT_PARAM_ERROR}\" \"参数 ${v_param_name} 必须是整数: ${v_number}\" \"validate_number\"\n    fi\n    \n    log_message \"${V_LOG_DEBUG}\" \"参数 ${v_param_name} 验证通过: ${v_number}\" \"validate_number\"\n}\n\n# 解析命令行参数\nparse_arguments() {\n    local v_num1_provided=false\n    local v_num2_provided=false\n    \n    while [[ $# -gt 0 ]]; do\n        case \"$1\" in\n            --num1)\n                if [[ -z \"${2:-}\" || \"${2}\" =~ ^- ]]; then\n                    error_exit \"${V_EXIT_PARAM_ERROR}\" \"参数 --num1 需要提供一个值\" \"parse_arguments\"\n                fi\n                V_NUM1=\"$2\"\n                v_num1_provided=true\n                shift 2\n                ;;\n            --num2)\n                if [[ -z \"${2:-}\" || \"${2}\" =~ ^- ]]; then\n                    error_exit \"${V_EXIT_PARAM_ERROR}\" \"参数 --num2 需要提供一个值\" \"parse_arguments\"\n                fi\n                V_NUM2=\"$2\"\n                v_num2_provided=true\n                shift 2\n                ;;\n            --version)\n                echo \"${V_SCRIPT_NAME} v${V_SCRIPT_VERSION}\"\n                exit \"${V_EXIT_SUCCESS}\"\n                ;;\n            --help)\n                show_usage\n                exit \"${V_EXIT_SUCCESS}\"\n                ;;\n            -*)\n                error_exit \"${V_EXIT_PARAM_ERROR}\" \"未知参数: $1\" \"parse_arguments\"\n                ;;\n            *)\n                error_exit \"${V_EXIT_PARAM_ERROR}\" \"无效参数: $1\" \"parse_arguments\"\n                ;;\n        esac\n    done\n    \n    # 检查必填参数\n    if [[ \"${v_num1_provided}\" != \"true\" ]]; then\n        error_exit \"${V_EXIT_PARAM_ERROR}\" \"缺少必填参数: --num1\" \"parse_arguments\"\n    fi\n    \n    if [[ \"${v_num2_provided}\" != \"true\" ]]; then\n        error_exit \"${V_EXIT_PARAM_ERROR}\" \"缺少必填参数: --num2\" \"parse_arguments\"\n    fi\n    \n    # 验证参数格式\n    validate_number \"${V_NUM1}\" \"--num1\"\n    validate_number \"${V_NUM2}\" \"--num2\"\n    \n    log_message \"${V_LOG_INFO}\" \"参数解析完成: num1=${V_NUM1}, num2=${V_NUM2}\" \"parse_arguments\"\n}\n\n# 计算两个数字的和\ncalculate_sum() {\n    local v_num1=\"$1\"\n    local v_num2=\"$2\"\n    local v_result\n    \n    log_message \"${V_LOG_DEBUG}\" \"开始计算: ${v_num1} + ${v_num2}\" \"calculate_sum\"\n    \n    # 使用bc进行安全计算\n    if ! v_result=$(echo \"${v_num1} + ${v_num2}\" | bc 2>/dev/null); then\n        error_exit \"${V_EXIT_CALC_ERROR}\" \"计算失败: ${v_num1} + ${v_num2}\" \"calculate_sum\"\n    fi\n    \n    # 检查计算结果是否为整数\n    if ! [[ \"${v_result}\" =~ ^-?[0-9]+$ ]]; then\n        error_exit \"${V_EXIT_CALC_ERROR}\" \"计算结果不是整数: ${v_result}\" \"calculate_sum\"\n    fi\n    \n    log_message \"${V_LOG_DEBUG}\" \"计算完成: ${v_num1} + ${v_num2} = ${v_result}\" \"calculate_sum\"\n    echo \"${v_result}\"\n}\n\n# 主执行函数\nmain() {\n    local v_start_time=$(date -u +\"%Y-%m-%dT%H:%M:%S.%3NZ\")\n    local v_execution_user=$(whoami)\n    local v_exit_code=\"${V_EXIT_SUCCESS}\"\n    \n    trap 'error_exit \"${V_EXIT_UNKNOWN_ERROR}\" \"脚本被中断\" \"main\"' INT TERM\n    \n    log_message \"${V_LOG_INFO}\" \"脚本启动\" \"main\"\n    log_message \"${V_LOG_DEBUG}\" \"开始时间: ${v_start_time}, 执行用户: ${v_execution_user}\" \"main\"\n    \n    # 环境检查\n    check_dependencies\n    \n    # 参数解析\n    parse_arguments \"$@\"\n    \n    # 执行计算\n    V_RESULT=$(calculate_sum \"${V_NUM1}\" \"${V_NUM2}\")\n    \n    # 输出结果\n    echo \"${V_RESULT}\"\n    \n    local v_end_time=$(date -u +\"%Y-%m-%dT%H:%M:%S.%3NZ\")\n    log_message \"${V_LOG_INFO}\" \"脚本执行成功, 结果: ${V_RESULT}\" \"main\"\n    log_message \"${V_LOG_DEBUG}\" \"结束时间: ${v_end_time}, 执行时长: $(($(date +%s) - $(date -d \"${v_start_time}\" +%s)))秒\" \"main\"\n    \n    exit \"${v_exit_code}\"\n}\n\n# 脚本入口\nmain \"$@\""