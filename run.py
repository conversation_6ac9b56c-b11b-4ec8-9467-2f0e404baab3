# -*- coding: UTF-8 -*-

import sys
import os
import shutil 
import uvicorn   
import json
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
# 仅在 环境变量 不存在时设置 setdefault
os.environ.setdefault("LOG_FILE_NAME", os.path.join(current_dir, "logs", "app.log")  )  
os.environ.setdefault("LOG_LEVEL", "INFO")  
os.environ.setdefault("SVC_PORT", "12705")  


def importPackages():
    from cloudevents.http import CloudEvent as baseCloudEvent, to_binary
    from dataclasses import dataclass
    from datetime import datetime, timedelta
    from fastapi import HTTPException
    from fastapi_cloudevents import CloudEvent
    from logging.handlers import RotatingFileHandler
    from multiprocessing import Manager
    from pydantic import BaseModel
    from typing import Optional, Dict, Any, Callable, List
    import asyncio
    import cloudevents
    import json
    import logging
    import multiprocessing
    import os
    import queue
    import requests
    import threading
    import traceback
    import urllib.parse
    import uuid
    import yaml





import argparse
if __name__ == "__main__":
    # 创建解析器并设置默认值（最低优先级）
    parser = argparse.ArgumentParser(description="启动服务")
    parser.add_argument("-p", "--port", type=int, default=12801, help="指定服务端口号，默认12801")
    parser.add_argument("-r", "--run", action="store_true", help="启动服务")
    parser.add_argument("-v", "--version", action="store_true", help="显示版本信息")
    args = parser.parse_args()

    # 1. 处理版本请求（命令行参数最高优先级）
    if args.version:
        from function.handler import get_version
        print(get_version())
        sys.exit(0)

    # 2. 端口优先级逻辑：命令行参数 > 环境变量 > 默认值
    port = args.port  # 优先从命令行获取
    if "SVC_PORT" in os.environ and not any(arg in sys.argv for arg in ["-p", "--port"]):
        # 仅当命令行未指定端口时，才使用环境变量
        port = int(os.environ["SVC_PORT"])

    # 3. 启动服务
    if args.run:
        importPackages()
        uvicorn.run("index:app", reload=False, host="0.0.0.0", port=port)
    else:
        print("错误：请使用 -r/--run 参数启动服务")
        sys.exit(1)