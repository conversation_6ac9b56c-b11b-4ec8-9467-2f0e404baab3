# -*- coding: UTF-8 -*-

# Copyright (c) <PERSON> 2019. All rights reserved.
# Licensed under the MIT license. See LICENSE file in the project root for full license information.
import json
from fastapi import FastAPI
from fastapi.openapi.utils import get_openapi
from function import handler
from starlette import status
from starlette.responses import HTMLResponse
from fastapi_cloudevents import CloudEvent, install_fastapi_cloudevents
import asyncio
from fastapi_cloudevents import CloudEvent

app = FastAPI(docs_url=None, redoc_url=None) 
app = install_fastapi_cloudevents(app)


def get_swagger_ui_html(
    *,
    openapi_spec: str,  # 注意：这里实际接收的是已序列化的 JSON 字符串
    title: str,
    swagger_js_url: str = "https://cdn.jsdelivr.net/npm/swagger-ui-dist@3/swagger-ui-bundle.js",
    swagger_css_url: str = "https://cdn.jsdelivr.net/npm/swagger-ui-dist@3/swagger-ui.css",
    swagger_favicon_url: str = "https://raw.githubusercontent.com/openfaas/docs/master/docs/images/favicon.ico",
) -> HTMLResponse:
    html = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <link type="text/css" rel="stylesheet" href="{swagger_css_url}">
        <link rel="shortcut icon" href="{swagger_favicon_url}">
        <title>{title}</title>
    </head>
    <body>
        <div id="swagger-ui"></div>
        <script src="{swagger_js_url}"></script>
        <script>
            const spec = {openapi_spec};  // 直接嵌入已序列化的 JSON，无需 JSON.parse
            const ui = SwaggerUIBundle({{
                spec: spec,
                dom_id: '#swagger-ui',
                presets: [
                    SwaggerUIBundle.presets.apis,
                    SwaggerUIBundle.SwaggerUIStandalonePreset
                ],
                layout: "BaseLayout",
                deepLinking: true
            }});
        </script>
    </body>
    </html>
    """
    return HTMLResponse(html)

    
def custom_openapi():
    """Generates schema from OpenFaas function particulars"""
    if app.openapi_schema:
        return app.openapi_schema

    # 确保生成的 OpenAPI 规范包含版本字段
    openapi_schema = get_openapi(
        title=handler.FUNCTION_NAME,
        version=f"v{handler.FUNCTION_VERSION}",
        routes=app.routes,
    )

    
    openapi_schema["openapi"] = "3.0.3"
    # 如果没有 openapi 版本字段，手动添加
    #if "openapi" not in openapi_schema:
    #    openapi_schema["openapi"] = "3.0.0"  # 或 "3.1.0"

    paths = openapi_schema["paths"]
    upd_paths = {}
    for key in paths:
        path = paths[key]
        for method in path:
            path[method]["tags"] = ["Function Definitions"]
        rel_path = f"/function/{handler.FUNCTION_NAME}"
        if key.startswith(rel_path):
            upd_paths[key] = path
        else:
            rel_path = f"{rel_path}{key}"
            upd_paths[rel_path] = path

    openapi_schema["paths"] = upd_paths
    app.openapi_schema = openapi_schema
    return app.openapi_schema


app.openapi = custom_openapi


@app.get("/")
async def swagger_ui_html():
    openapi_spec = json.dumps(app.openapi())  # 序列化为 JSON 字符串
    return get_swagger_ui_html(
        openapi_spec=openapi_spec,  # 直接传递字符串
        title=f"fastapi cloudevent function: {handler.FUNCTION_NAME.title()}",
    )





@app.post(
    "/{path:path}",
    status_code=status.HTTP_200_OK,
    response_model=handler.ResponseModel,
    summary=handler.FUNCTION_SUMMARY,
    response_description=handler.FUNCTION_RESPONSE_DESC,
)
async def handle_request(
    *,
    event: CloudEvent,
) -> CloudEvent:
    asynctask = asyncio.create_task(handler.handle(event))
    await asynctask 
    res = asynctask.result()
    
    return res


@app.get("/swagger.json", include_in_schema=False)
async def swagger_json():
    return app.openapi()
